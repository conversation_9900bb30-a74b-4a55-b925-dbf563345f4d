import 'package:flutter/material.dart';
import 'app_routes.dart';
import '../../presentation/pages/splash_page.dart';
import '../../presentation/pages/onboarding_page.dart';
import '../../presentation/pages/main_navigation_page.dart';
import '../../presentation/pages/home_page.dart';
import '../../presentation/pages/profile_page.dart';
import '../../presentation/pages/goals_page.dart';
import '../../presentation/pages/camera_page.dart';
import '../../presentation/pages/food_analysis_page.dart';
import '../../presentation/pages/daily_log_page.dart';
import '../../presentation/pages/history_page.dart';
import '../../presentation/pages/settings_page.dart';
import '../../presentation/pages/add_food_page.dart';
import '../../presentation/pages/analytics_page.dart';
import '../../presentation/pages/meal_detail_page.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashPage());

      case AppRoutes.onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingPage());

      case AppRoutes.main:
        return MaterialPageRoute(builder: (_) => const MainNavigationPage());

      case AppRoutes.home:
        return MaterialPageRoute(builder: (_) => const HomePage());

      case AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const ProfilePage());

      case AppRoutes.goals:
        return MaterialPageRoute(builder: (_) => const GoalsPage());

      case AppRoutes.camera:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) =>
              CameraPage(mealType: args?['mealType'] as String? ?? 'snack'),
        );

      case AppRoutes.foodAnalysis:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => FoodAnalysisPage(
            imagePath: args['imagePath'] as String,
            mealType: args['mealType'] as String,
          ),
        );

      case AppRoutes.dailyLog:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) =>
              DailyLogPage(selectedDate: args?['selectedDate'] as DateTime?),
        );

      case AppRoutes.history:
        return MaterialPageRoute(builder: (_) => const HistoryPage());

      case AppRoutes.settings:
        return MaterialPageRoute(builder: (_) => const SettingsPage());

      case AppRoutes.addFood:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) =>
              AddFoodPage(mealType: args?['mealType'] as String? ?? 'snack'),
        );

      case AppRoutes.editFood:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => AddFoodPage(
            mealType: args['mealType'] as String,
            foodEntry: args['foodEntry'],
          ),
        );

      case AppRoutes.mealDetail:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => MealDetailPage(foodEntry: args['foodEntry']),
        );

      case AppRoutes.analytics:
        return MaterialPageRoute(builder: (_) => const AnalyticsPage());

      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: const Center(child: Text('Page not found')),
      ),
    );
  }
}
