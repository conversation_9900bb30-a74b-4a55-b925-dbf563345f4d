import 'package:flutter/material.dart';
import '../../domain/entities/food_entry.dart';
import '../../domain/entities/nutritional_goals.dart';
import '../../domain/usecases/get_daily_nutrition_usecase.dart';
import '../../domain/repositories/food_entry_repository.dart';
import '../../core/utils/date_utils.dart';

class NutritionProvider extends ChangeNotifier {
  final GetDailyNutritionUseCase _getDailyNutritionUseCase;
  final FoodEntryRepository _foodEntryRepository;

  NutritionProvider(this._getDailyNutritionUseCase, this._foodEntryRepository);

  bool _isLoading = false;
  String? _error;
  DailyNutritionData? _currentDayData;
  DateTime _selectedDate = DateTime.now();
  List<FoodEntry> _recentEntries = [];

  bool get isLoading => _isLoading;
  String? get error => _error;
  DailyNutritionData? get currentDayData => _currentDayData;
  DateTime get selectedDate => _selectedDate;
  List<FoodEntry> get recentEntries => _recentEntries;

  NutritionalGoals? get nutritionalGoals => _currentDayData?.nutritionalGoals;
  Map<String, double> get progressPercentages =>
      _currentDayData?.progressPercentages ?? {};
  Map<String, List<FoodEntry>> get mealGroups =>
      _currentDayData?.mealGroups ?? {};

  // Weekly data for analytics
  List<FoodEntry> _weeklyEntries = [];
  List<FoodEntry> get weeklyEntries => _weeklyEntries;

  Future<void> loadDailyNutrition(String userId, {DateTime? date}) async {
    _setLoading(true);
    _clearError();

    try {
      final targetDate = date ?? _selectedDate;
      final params = GetDailyNutritionParams(userId: userId, date: targetDate);

      final result = await _getDailyNutritionUseCase.execute(params);

      if (result.isSuccess) {
        _currentDayData = result.successValue;
        _selectedDate = targetDate;
      } else {
        _setError(
          result.failureValue?.message ?? 'Failed to load nutrition data',
        );
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadRecentEntries(String userId) async {
    try {
      final result = await _foodEntryRepository.getFoodEntriesByUser(userId);
      if (result.isSuccess) {
        _recentEntries = List<FoodEntry>.from(result.successValue!.take(10));
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading recent entries: $e');
    }
  }

  Future<void> addFoodEntry(FoodEntry entry) async {
    try {
      final result = await _foodEntryRepository.createFoodEntry(entry);
      if (result.isSuccess) {
        // Reload current day data if the entry is for the selected date
        if (AppDateUtils.isSameDay(entry.consumedAt, _selectedDate)) {
          await loadDailyNutrition(entry.userId, date: _selectedDate);
        }
        // Update recent entries
        _recentEntries.insert(0, entry);
        if (_recentEntries.length > 10) {
          _recentEntries = List<FoodEntry>.from(_recentEntries.take(10));
        }
        notifyListeners();
      } else {
        _setError(result.failureValue?.message ?? 'Failed to add food entry');
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    }
  }

  Future<void> updateFoodEntry(FoodEntry entry) async {
    try {
      final result = await _foodEntryRepository.updateFoodEntry(entry);
      if (result.isSuccess) {
        // Reload current day data if the entry is for the selected date
        if (AppDateUtils.isSameDay(entry.consumedAt, _selectedDate)) {
          await loadDailyNutrition(entry.userId, date: _selectedDate);
        }
        // Update recent entries
        final index = _recentEntries.indexWhere((e) => e.id == entry.id);
        if (index != -1) {
          _recentEntries[index] = entry;
          notifyListeners();
        }
      } else {
        _setError(
          result.failureValue?.message ?? 'Failed to update food entry',
        );
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    }
  }

  Future<void> deleteFoodEntry(String entryId, String userId) async {
    try {
      final result = await _foodEntryRepository.deleteFoodEntry(entryId);
      if (result.isSuccess) {
        // Reload current day data
        await loadDailyNutrition(userId, date: _selectedDate);
        // Remove from recent entries
        _recentEntries.removeWhere((entry) => entry.id == entryId);
        notifyListeners();
      } else {
        _setError(
          result.failureValue?.message ?? 'Failed to delete food entry',
        );
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    }
  }

  void selectDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  // Monthly and quarterly data
  List<FoodEntry> _monthlyEntries = [];
  List<FoodEntry> _quarterlyEntries = [];

  List<FoodEntry> get monthlyEntries => _monthlyEntries;
  List<FoodEntry> get quarterlyEntries => _quarterlyEntries;

  Future<void> loadWeeklyData(String userId) async {
    try {
      final now = DateTime.now();
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final endOfWeek = startOfWeek.add(const Duration(days: 6));

      final result = await _foodEntryRepository.getFoodEntriesByDateRange(
        userId,
        startOfWeek,
        endOfWeek,
      );

      if (result.isSuccess) {
        _weeklyEntries = result.successValue!;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading weekly data: $e');
    }
  }

  Future<void> loadMonthlyData(String userId) async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      final result = await _foodEntryRepository.getFoodEntriesByDateRange(
        userId,
        startOfMonth,
        endOfMonth,
      );

      if (result.isSuccess) {
        _monthlyEntries = result.successValue!;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading monthly data: $e');
    }
  }

  Future<void> loadQuarterlyData(String userId) async {
    try {
      final now = DateTime.now();
      final startOfQuarter = now.subtract(const Duration(days: 90));

      final result = await _foodEntryRepository.getFoodEntriesByDateRange(
        userId,
        startOfQuarter,
        now,
      );

      if (result.isSuccess) {
        _quarterlyEntries = result.successValue!;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading quarterly data: $e');
    }
  }

  List<double> getWeeklyCalorieData() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    final weeklyCalories = <double>[];

    for (int i = 0; i < 7; i++) {
      final date = startOfWeek.add(Duration(days: i));
      final dayEntries = _weeklyEntries
          .where((entry) => AppDateUtils.isSameDay(entry.consumedAt, date))
          .toList();

      final totalCalories = dayEntries.fold<double>(
        0,
        (sum, entry) => sum + entry.nutritionData.calories,
      );

      weeklyCalories.add(totalCalories);
    }

    return weeklyCalories;
  }

  Map<String, double> getWeeklyMacroBreakdown() {
    final totalProtein = _weeklyEntries.fold<double>(
      0,
      (sum, entry) => sum + entry.nutritionData.protein,
    );
    final totalCarbs = _weeklyEntries.fold<double>(
      0,
      (sum, entry) => sum + entry.nutritionData.carbs,
    );
    final totalFats = _weeklyEntries.fold<double>(
      0,
      (sum, entry) => sum + entry.nutritionData.fats,
    );

    return {'protein': totalProtein, 'carbs': totalCarbs, 'fats': totalFats};
  }

  List<List<FoodEntry>> getWeeklyGroupedData() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    final groupedData = <List<FoodEntry>>[];
    for (int i = 0; i < 7; i++) {
      final date = startOfWeek.add(Duration(days: i));
      final dayEntries = _weeklyEntries
          .where((entry) => AppDateUtils.isSameDay(entry.consumedAt, date))
          .toList();
      groupedData.add(dayEntries);
    }
    return groupedData;
  }

  List<List<FoodEntry>> getMonthlyGroupedData() {
    final now = DateTime.now();
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;

    final groupedData = <List<FoodEntry>>[];
    for (int i = 1; i <= daysInMonth; i++) {
      final date = DateTime(now.year, now.month, i);
      final dayEntries = _monthlyEntries
          .where((entry) => AppDateUtils.isSameDay(entry.consumedAt, date))
          .toList();
      groupedData.add(dayEntries);
    }
    return groupedData;
  }

  List<List<FoodEntry>> getQuarterlyGroupedData() {
    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 90));

    final groupedData = <List<FoodEntry>>[];
    // Group by weeks for quarterly view
    for (int i = 0; i < 13; i++) {
      final weekStart = startDate.add(Duration(days: i * 7));
      final weekEnd = weekStart.add(const Duration(days: 6));

      final weekEntries = _quarterlyEntries.where((entry) {
        return entry.consumedAt.isAfter(
              weekStart.subtract(const Duration(days: 1)),
            ) &&
            entry.consumedAt.isBefore(weekEnd.add(const Duration(days: 1)));
      }).toList();

      groupedData.add(weekEntries);
    }
    return groupedData;
  }

  Map<String, double> getMealTimingData() {
    final entries = _weeklyEntries.isNotEmpty
        ? _weeklyEntries
        : _monthlyEntries.isNotEmpty
        ? _monthlyEntries
        : _quarterlyEntries;

    final mealCalories = <String, List<double>>{
      'breakfast': [],
      'lunch': [],
      'dinner': [],
      'snack': [],
    };

    // Group entries by date and meal type
    final dateGroups = <String, List<FoodEntry>>{};
    for (final entry in entries) {
      final dateKey = AppDateUtils.formatDateAsString(entry.consumedAt);
      dateGroups[dateKey] ??= [];
      dateGroups[dateKey]!.add(entry);
    }

    // Calculate daily calories per meal type
    for (final dayEntries in dateGroups.values) {
      final dailyMealCalories = <String, double>{
        'breakfast': 0,
        'lunch': 0,
        'dinner': 0,
        'snack': 0,
      };

      for (final entry in dayEntries) {
        dailyMealCalories[entry.mealType] =
            (dailyMealCalories[entry.mealType] ?? 0) +
            entry.nutritionData.calories;
      }

      for (final mealType in dailyMealCalories.keys) {
        mealCalories[mealType]!.add(dailyMealCalories[mealType]!);
      }
    }

    // Calculate averages
    final averages = <String, double>{};
    for (final mealType in mealCalories.keys) {
      final calories = mealCalories[mealType]!;
      averages[mealType] = calories.isNotEmpty
          ? calories.reduce((a, b) => a + b) / calories.length
          : 0;
    }

    return averages;
  }

  Map<String, dynamic> getFoodVarietyData() {
    final entries = _weeklyEntries.isNotEmpty
        ? _weeklyEntries
        : _monthlyEntries.isNotEmpty
        ? _monthlyEntries
        : _quarterlyEntries;

    if (entries.isEmpty) {
      return {
        'uniqueFoods': 0,
        'categories': 0,
        'avgMealsPerDay': 0.0,
        'mostCommonFood': 'None',
      };
    }

    // Count unique foods
    final uniqueFoods = entries.map((e) => e.name.toLowerCase()).toSet();

    // Count food categories (simplified - could be enhanced with actual categorization)
    final categories = entries.map((e) => e.mealType).toSet();

    // Calculate average meals per day
    final dateGroups = <String, int>{};
    for (final entry in entries) {
      final dateKey = AppDateUtils.formatDateAsString(entry.consumedAt);
      dateGroups[dateKey] = (dateGroups[dateKey] ?? 0) + 1;
    }

    final avgMealsPerDay = dateGroups.isNotEmpty
        ? dateGroups.values.reduce((a, b) => a + b) / dateGroups.length
        : 0.0;

    // Find most common food
    final foodCounts = <String, int>{};
    for (final entry in entries) {
      final foodName = entry.name.toLowerCase();
      foodCounts[foodName] = (foodCounts[foodName] ?? 0) + 1;
    }

    final mostCommonFood = foodCounts.isNotEmpty
        ? foodCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : 'None';

    return {
      'uniqueFoods': uniqueFoods.length,
      'categories': categories.length,
      'avgMealsPerDay': avgMealsPerDay,
      'mostCommonFood': mostCommonFood,
    };
  }
}
