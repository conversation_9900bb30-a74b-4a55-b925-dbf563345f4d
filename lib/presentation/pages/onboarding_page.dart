import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/custom_card.dart';
import '../widgets/animated_progress_indicator.dart';
import '../widgets/onboarding_background.dart';
import '../widgets/success_animation.dart';

import '../../domain/usecases/create_user_profile_usecase.dart';
import '../providers/app_provider.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = false;
  
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Form controllers
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();

  // Form values
  String _gender = 'male';
  String _activityLevel = 'moderately_active';
  String _goal = 'maintain';
  String _weightUnit = 'kg';
  String _heightUnit = 'cm';

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: AppConstants.mediumAnimationDuration,
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: AppConstants.mediumAnimationDuration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _nextPage() {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    if (_currentPage < 2) {
      _fadeController.reset();
      _slideController.reset();
      _pageController.nextPage(
        duration: AppConstants.mediumAnimationDuration,
        curve: Curves.easeInOut,
      );
      Future.delayed(const Duration(milliseconds: 100), () {
        _fadeController.forward();
        _slideController.forward();
      });
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: AppConstants.mediumAnimationDuration,
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateFormFields() {
    // Validate name
    if (_nameController.text.trim().isEmpty) {
      _showError('Please enter your name');
      return false;
    }

    // Validate age
    final age = int.tryParse(_ageController.text);
    if (age == null || age < 13 || age > 120) {
      _showError('Please enter a valid age (13-120)');
      return false;
    }

    // Validate weight
    final weight = double.tryParse(_weightController.text);
    if (weight == null || weight <= 0) {
      _showError('Please enter a valid weight');
      return false;
    }

    // Validate height
    final height = double.tryParse(_heightController.text);
    if (height == null || height <= 0) {
      _showError('Please enter a valid height');
      return false;
    }

    return true;
  }

  Future<void> _completeOnboarding() async {
    // Validate form fields manually since the form key is only on page 1
    if (!_validateFormFields()) return;

    setState(() => _isLoading = true);

    try {
      final appProvider = context.read<AppProvider>();
      final createUserProfileUseCase = Provider.of<CreateUserProfileUseCase>(
        context,
        listen: false,
      );

      // API key is now hardcoded in constants
      final params = CreateUserProfileParams(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        age: int.parse(_ageController.text),
        gender: _gender,
        weight: double.parse(_weightController.text),
        height: double.parse(_heightController.text),
        activityLevel: _activityLevel,
        goal: _goal,
        weightUnit: _weightUnit,
        heightUnit: _heightUnit,
      );

      final result = await createUserProfileUseCase.execute(params);

      if (result.isSuccess && result.successValue != null) {
        await appProvider.completeOnboarding(result.successValue!);

        if (mounted) {
          // Show success animation before navigating
          _showSuccessAnimation();
        }
      } else {
        _showError(result.failureValue?.message ?? 'Failed to create profile');
      }
    } catch (e) {
      _showError('An unexpected error occurred: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSuccessAnimation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: SuccessAnimation(
          onComplete: () {
            Navigator.of(context).pop(); // Close dialog
            Navigator.of(context).pushReplacementNamed(AppRoutes.main);
          },
        ),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: OnboardingBackground(
        currentPage: _currentPage,
        child: SafeArea(
          child: Column(
            children: [
              // Modern animated progress indicator
              AnimatedProgressIndicator(
                currentStep: _currentPage,
                totalSteps: 3,
              ),

              // Page content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (page) {
                    setState(() => _currentPage = page);
                    _fadeController.reset();
                    _slideController.reset();
                    Future.delayed(const Duration(milliseconds: 100), () {
                      _fadeController.forward();
                      _slideController.forward();
                    });
                  },
                  children: [
                    _buildWelcomePage(),
                    _buildPersonalInfoPage(),
                    _buildGoalsPage(),
                  ],
                ),
              ),

              // Navigation buttons
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildWelcomePage() {
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              const SizedBox(height: AppConstants.largePadding),
              
              // Hero illustration
              Container(
                width: 160,
                height: 160,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.2),
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                      Colors.transparent,
                    ],
                  ),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.primary.withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.restaurant_menu,
                      size: 50,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: AppConstants.largePadding),

              Text(
                'Welcome to ${AppConstants.appName}',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              Text(
                'Track your nutrition with AI-powered food analysis.\nTake photos of your meals and get detailed nutritional information instantly.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.largePadding),

              // Feature cards with modern design
              _buildFeatureCards(theme),
              
              const SizedBox(height: AppConstants.largePadding),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCards(ThemeData theme) {
    final features = [
      {
        'icon': Icons.camera_alt_outlined,
        'title': 'AI Photo Analysis',
        'description': 'Snap a photo and get instant nutrition facts',
        'color': theme.colorScheme.primary,
      },
      {
        'icon': Icons.track_changes_outlined,
        'title': 'Smart Tracking',
        'description': 'Monitor your daily nutrition goals effortlessly',
        'color': theme.colorScheme.secondary,
      },
      {
        'icon': Icons.analytics_outlined,
        'title': 'Detailed Insights',
        'description': 'Get comprehensive nutrition analytics',
        'color': theme.colorScheme.tertiary,
      },
    ];

    return Column(
      children: features.map((feature) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: CustomCard(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            backgroundColor: theme.colorScheme.surface,
            elevation: 1,
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: (feature['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    feature['icon'] as IconData,
                    color: feature['color'] as Color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feature['title'] as String,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        feature['description'] as String,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }



  Widget _buildPersonalInfoPage() {
    final theme = Theme.of(context);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with icon
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.person_outline,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Personal Information',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          Text(
                            'Help us personalize your nutrition goals',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // Name field with modern styling
                        _buildModernTextField(
                          controller: _nameController,
                          label: 'Full Name',
                          icon: Icons.person_outline,
                          validator: (value) {
                            if (value?.trim().isEmpty ?? true) {
                              return 'Please enter your name';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: AppConstants.defaultPadding),

                        // Age and Gender row
                        Row(
                          children: [
                            Expanded(
                              child: _buildModernTextField(
                                controller: _ageController,
                                label: 'Age',
                                icon: Icons.cake_outlined,
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  final age = int.tryParse(value ?? '');
                                  if (age == null || age < 13 || age > 120) {
                                    return 'Enter valid age (13-120)';
                                  }
                                  return null;
                                },
                              ),
                            ),

                            const SizedBox(width: AppConstants.defaultPadding),

                            Expanded(
                              child: _buildModernDropdown(
                                value: _gender,
                                label: 'Gender',
                                icon: Icons.wc_outlined,
                                items: const [
                                  DropdownMenuItem(value: 'male', child: Text('Male')),
                                  DropdownMenuItem(value: 'female', child: Text('Female')),
                                ],
                                onChanged: (value) => setState(() => _gender = value!),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: AppConstants.defaultPadding),

                        // Weight row
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: _buildModernTextField(
                                controller: _weightController,
                                label: 'Weight',
                                icon: Icons.monitor_weight_outlined,
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  final weight = double.tryParse(value ?? '');
                                  if (weight == null || weight <= 0) {
                                    return 'Enter valid weight';
                                  }
                                  return null;
                                },
                              ),
                            ),

                            const SizedBox(width: AppConstants.smallPadding),

                            Expanded(
                              child: _buildModernDropdown(
                                value: _weightUnit,
                                label: 'Unit',
                                items: const [
                                  DropdownMenuItem(value: 'kg', child: Text('kg')),
                                  DropdownMenuItem(value: 'lbs', child: Text('lbs')),
                                ],
                                onChanged: (value) => setState(() => _weightUnit = value!),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: AppConstants.defaultPadding),

                        // Height row
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: _buildModernTextField(
                                controller: _heightController,
                                label: 'Height',
                                icon: Icons.height_outlined,
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  final height = double.tryParse(value ?? '');
                                  if (height == null || height <= 0) {
                                    return 'Enter valid height';
                                  }
                                  return null;
                                },
                              ),
                            ),

                            const SizedBox(width: AppConstants.smallPadding),

                            Expanded(
                              child: _buildModernDropdown(
                                value: _heightUnit,
                                label: 'Unit',
                                items: const [
                                  DropdownMenuItem(value: 'cm', child: Text('cm')),
                                  DropdownMenuItem(value: 'ft', child: Text('ft')),
                                ],
                                onChanged: (value) => setState(() => _heightUnit = value!),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);
    
    return AnimatedContainer(
      duration: AppConstants.shortAnimationDuration,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Focus(
        child: Builder(
          builder: (context) {
            final hasFocus = Focus.of(context).hasFocus;
            
            return AnimatedContainer(
              duration: AppConstants.shortAnimationDuration,
              transform: Matrix4.identity()..scale(hasFocus ? 1.02 : 1.0),
              child: TextFormField(
                controller: controller,
                keyboardType: keyboardType,
                validator: validator,
                style: theme.textTheme.bodyLarge,
                decoration: InputDecoration(
                  labelText: label,
                  prefixIcon: AnimatedContainer(
                    duration: AppConstants.shortAnimationDuration,
                    child: Icon(
                      icon, 
                      color: hasFocus 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(
                      color: theme.colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(
                      color: theme.colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(
                      color: theme.colorScheme.error,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildModernDropdown<T>({
    required T value,
    required String label,
    IconData? icon,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<T>(
        value: value,
        items: items,
        onChanged: onChanged,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: icon != null ? Icon(icon, color: theme.colorScheme.primary) : null,
          filled: true,
          fillColor: theme.colorScheme.surface,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.primary,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildGoalsPage() {
    final theme = Theme.of(context);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.flag_outlined,
                      color: theme.colorScheme.secondary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Goals',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          'Tell us about your activity level and goals',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Activity Level Section
                      _buildSectionHeader(
                        'Activity Level',
                        Icons.directions_run_outlined,
                        theme.colorScheme.primary,
                      ),

                      const SizedBox(height: AppConstants.defaultPadding),

                      ...ActivityLevel.values.map(
                        (level) => _buildModernRadioTile(
                          title: level.displayName,
                          subtitle: level.description,
                          value: level.value,
                          groupValue: _activityLevel,
                          onChanged: (value) {
                            HapticFeedback.selectionClick();
                            setState(() => _activityLevel = value!);
                          },
                          icon: _getActivityIcon(level),
                        ),
                      ),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Goal Section
                      _buildSectionHeader(
                        'Your Goal',
                        Icons.track_changes_outlined,
                        theme.colorScheme.secondary,
                      ),

                      const SizedBox(height: AppConstants.defaultPadding),

                      ...Goal.values.map(
                        (goal) => _buildModernRadioTile(
                          title: goal.displayName,
                          subtitle: goal.description,
                          value: goal.value,
                          groupValue: _goal,
                          onChanged: (value) {
                            HapticFeedback.selectionClick();
                            setState(() => _goal = value!);
                          },
                          icon: _getGoalIcon(goal),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: AppConstants.smallPadding),
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildModernRadioTile<T>({
    required String title,
    required String subtitle,
    required T value,
    required T groupValue,
    required void Function(T?) onChanged,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    final isSelected = value == groupValue;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onChanged(value),
          borderRadius: BorderRadius.circular(16),
          child: AnimatedContainer(
            duration: AppConstants.shortAnimationDuration,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: isSelected 
                  ? theme.colorScheme.primary.withValues(alpha: 0.1)
                  : theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withValues(alpha: 0.2),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? theme.colorScheme.primary
                        : theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected 
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                AnimatedScale(
                  scale: isSelected ? 1.0 : 0.8,
                  duration: AppConstants.shortAnimationDuration,
                  child: Icon(
                    isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
                    color: isSelected 
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getActivityIcon(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return Icons.chair_outlined;
      case ActivityLevel.lightlyActive:
        return Icons.directions_walk_outlined;
      case ActivityLevel.moderatelyActive:
        return Icons.directions_run_outlined;
      case ActivityLevel.veryActive:
        return Icons.fitness_center_outlined;
      case ActivityLevel.extremelyActive:
        return Icons.sports_gymnastics_outlined;
    }
  }

  IconData _getGoalIcon(Goal goal) {
    switch (goal) {
      case Goal.lose:
        return Icons.trending_down_outlined;
      case Goal.maintain:
        return Icons.trending_flat_outlined;
      case Goal.gain:
        return Icons.trending_up_outlined;
    }
  }

  Widget _buildGetStartedButton() {
    final theme = Theme.of(context);
    
    return Container(
      key: const ValueKey('get_started'),
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.secondary,
          ],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _nextPage,
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Setting up...',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.rocket_launch_outlined,
                    color: theme.colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Get Started',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            if (_currentPage > 0) ...[
              Expanded(
                child: CustomButton(
                  text: 'Back',
                  onPressed: _previousPage,
                  isOutlined: true,
                  icon: Icons.arrow_back,
                  height: 56,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
            ],

            Expanded(
              flex: _currentPage > 0 ? 1 : 1,
              child: AnimatedSwitcher(
                duration: AppConstants.mediumAnimationDuration,
                child: _currentPage == 2
                    ? _buildGetStartedButton()
                    : CustomButton(
                        key: const ValueKey('continue'),
                        text: 'Continue',
                        onPressed: _nextPage,
                        isLoading: _isLoading,
                        icon: Icons.arrow_forward,
                        height: 56,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum ActivityLevel {
  sedentary('sedentary', 'Sedentary', 'Little to no exercise'),
  lightlyActive(
    'lightly_active',
    'Lightly Active',
    'Light exercise 1-3 days/week',
  ),
  moderatelyActive(
    'moderately_active',
    'Moderately Active',
    'Moderate exercise 3-5 days/week',
  ),
  veryActive('very_active', 'Very Active', 'Hard exercise 6-7 days/week'),
  extremelyActive(
    'extremely_active',
    'Extremely Active',
    'Very hard exercise, physical job',
  );

  const ActivityLevel(this.value, this.displayName, this.description);
  final String value;
  final String displayName;
  final String description;
}

enum Goal {
  lose('lose', 'Lose Weight', 'Create a caloric deficit'),
  maintain('maintain', 'Maintain Weight', 'Maintain current weight'),
  gain('gain', 'Gain Weight', 'Create a caloric surplus');

  const Goal(this.value, this.displayName, this.description);
  final String value;
  final String displayName;
  final String description;
}
