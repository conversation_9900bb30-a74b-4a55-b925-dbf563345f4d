import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../../domain/entities/food_entry.dart';

class MealDetailPage extends StatelessWidget {
  final FoodEntry foodEntry;

  const MealDetailPage({super.key, required this.foodEntry});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(foodEntry.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editFoodEntry(context),
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _deleteFoodEntry(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Food Image and Basic Info
            _buildFoodHeader(context),

            const SizedBox(height: AppConstants.defaultPadding),

            // Nutrition Overview
            _buildNutritionOverview(context),

            const SizedBox(height: AppConstants.defaultPadding),

            // Detailed Nutrition
            _buildDetailedNutrition(context),

            const SizedBox(height: AppConstants.defaultPadding),

            // Meal Information
            _buildMealInformation(context),

            const SizedBox(height: AppConstants.defaultPadding),

            // AI Analysis Info (if available)
            if (foodEntry.isAnalyzedByAI) _buildAIAnalysisInfo(context),

            const SizedBox(height: AppConstants.defaultPadding * 2),

            // Action Buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFoodHeader(BuildContext context) {
    final theme = Theme.of(context);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Food Image or Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadius,
                  ),
                ),
                child: foodEntry.imagePath != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius,
                        ),
                        child: Image.network(
                          foodEntry.imagePath!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.restaurant,
                            size: 40,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.restaurant,
                        size: 40,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
              ),

              const SizedBox(width: AppConstants.defaultPadding),

              // Food Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      foodEntry.name,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    if (foodEntry.brand != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        foodEntry.brand!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],

                    const SizedBox(height: 8),

                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getMealTypeColor(context),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        foodEntry.mealType.toUpperCase(),
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (foodEntry.description != null) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              foodEntry.description!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNutritionOverview(BuildContext context) {
    final theme = Theme.of(context);
    final nutrition = foodEntry.nutritionData;

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Nutrition Overview',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Main macros
          Row(
            children: [
              Expanded(
                child: _buildNutritionItem(
                  context,
                  'Calories',
                  nutrition.calories.toStringAsFixed(0),
                  'kcal',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildNutritionItem(
                  context,
                  'Protein',
                  nutrition.protein.toStringAsFixed(1),
                  'g',
                  Icons.fitness_center,
                  Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.smallPadding),

          Row(
            children: [
              Expanded(
                child: _buildNutritionItem(
                  context,
                  'Carbs',
                  nutrition.carbs.toStringAsFixed(1),
                  'g',
                  Icons.grain,
                  Colors.amber,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildNutritionItem(
                  context,
                  'Fats',
                  nutrition.fats.toStringAsFixed(1),
                  'g',
                  Icons.opacity,
                  Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(
    BuildContext context,
    String label,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(unit, style: theme.textTheme.bodySmall?.copyWith(color: color)),
          const SizedBox(height: 2),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Color _getMealTypeColor(BuildContext context) {
    switch (foodEntry.mealType.toLowerCase()) {
      case 'breakfast':
        return Colors.orange;
      case 'lunch':
        return Colors.green;
      case 'dinner':
        return Colors.blue;
      case 'snack':
        return Colors.purple;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  void _editFoodEntry(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/edit-food',
      arguments: {'foodEntry': foodEntry, 'mealType': foodEntry.mealType},
    );
  }

  Widget _buildDetailedNutrition(BuildContext context) {
    final theme = Theme.of(context);
    final nutrition = foodEntry.nutritionData;

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Detailed Nutrition',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          _buildNutritionRow(
            context,
            'Fiber',
            '${nutrition.fiber.toStringAsFixed(1)}g',
          ),
          _buildNutritionRow(
            context,
            'Sugar',
            '${nutrition.sugar.toStringAsFixed(1)}g',
          ),
          _buildNutritionRow(
            context,
            'Sodium',
            '${nutrition.sodium.toStringAsFixed(0)}mg',
          ),
          _buildNutritionRow(
            context,
            'Saturated Fat',
            '${nutrition.saturatedFat.toStringAsFixed(1)}g',
          ),
          _buildNutritionRow(
            context,
            'Cholesterol',
            '${nutrition.cholesterol.toStringAsFixed(0)}mg',
          ),
          _buildNutritionRow(
            context,
            'Potassium',
            '${nutrition.potassium.toStringAsFixed(0)}mg',
          ),

          const Divider(),

          Text(
            'Vitamins & Minerals',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          _buildNutritionRow(
            context,
            'Vitamin A',
            '${nutrition.vitaminA.toStringAsFixed(0)}mcg',
          ),
          _buildNutritionRow(
            context,
            'Vitamin C',
            '${nutrition.vitaminC.toStringAsFixed(0)}mg',
          ),
          _buildNutritionRow(
            context,
            'Vitamin D',
            '${nutrition.vitaminD.toStringAsFixed(0)}mcg',
          ),
          _buildNutritionRow(
            context,
            'Calcium',
            '${nutrition.calcium.toStringAsFixed(0)}mg',
          ),
          _buildNutritionRow(
            context,
            'Iron',
            '${nutrition.iron.toStringAsFixed(1)}mg',
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: theme.textTheme.bodyMedium),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealInformation(BuildContext context) {
    final theme = Theme.of(context);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Meal Information',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          _buildInfoRow(
            context,
            'Serving Size',
            '${foodEntry.servingSize} ${foodEntry.servingUnit}',
            Icons.straighten,
          ),

          _buildInfoRow(
            context,
            'Consumed At',
            DateFormat('MMM dd, yyyy hh:mm a').format(foodEntry.consumedAt),
            Icons.access_time,
          ),

          _buildInfoRow(
            context,
            'Added On',
            DateFormat('MMM dd, yyyy hh:mm a').format(foodEntry.createdAt),
            Icons.calendar_today,
          ),

          if (foodEntry.notes != null && foodEntry.notes!.isNotEmpty) ...[
            const SizedBox(height: AppConstants.smallPadding),
            const Divider(),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Notes',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              foodEntry.notes!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: theme.colorScheme.onSurfaceVariant),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(child: Text(label, style: theme.textTheme.bodyMedium)),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIAnalysisInfo(BuildContext context) {
    final theme = Theme.of(context);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.psychology,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                'AI Analysis',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          if (foodEntry.confidence != null) ...[
            _buildInfoRow(
              context,
              'Confidence Score',
              '${(foodEntry.confidence! * 100).toStringAsFixed(0)}%',
              Icons.verified,
            ),
          ],

          Container(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'This food was analyzed using AI image recognition',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Edit Entry',
            icon: Icons.edit,
            onPressed: () => _editFoodEntry(context),
            isOutlined: true,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: CustomButton(
            text: 'Delete Entry',
            icon: Icons.delete,
            onPressed: () => _deleteFoodEntry(context),
            backgroundColor: Colors.red,
          ),
        ),
      ],
    );
  }

  void _deleteFoodEntry(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Food Entry'),
        content: Text('Are you sure you want to delete "${foodEntry.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to previous screen
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${foodEntry.name} deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
