import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../providers/nutrition_provider.dart';
import '../providers/app_provider.dart';
import '../../domain/entities/nutritional_goals.dart';
import '../../domain/entities/food_entry.dart';

class AnalyticsPage extends StatefulWidget {
  const AnalyticsPage({super.key});

  @override
  State<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends State<AnalyticsPage> {
  String _selectedPeriod = 'Week';
  final List<String> _periods = ['Week', 'Month', '3 Months'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAnalyticsData();
    });
  }

  void _loadAnalyticsData() {
    final appProvider = context.read<AppProvider>();
    final nutritionProvider = context.read<NutritionProvider>();

    if (appProvider.currentUser != null) {
      switch (_selectedPeriod) {
        case 'Week':
          nutritionProvider.loadWeeklyData(appProvider.currentUser!.id);
          break;
        case 'Month':
          nutritionProvider.loadMonthlyData(appProvider.currentUser!.id);
          break;
        case '3 Months':
          nutritionProvider.loadQuarterlyData(appProvider.currentUser!.id);
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NutritionProvider>(
      builder: (context, nutritionProvider, child) {
        final goals = nutritionProvider.nutritionalGoals;
        final progress = nutritionProvider.progressPercentages;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeaderSection(context),

              const SizedBox(height: AppConstants.defaultPadding),

              // Period selector
              _buildPeriodSelector(context),

              const SizedBox(height: AppConstants.defaultPadding),

              if (goals != null) ...[
                // Today's overview
                _buildTodaysOverview(context, goals, progress),

                const SizedBox(height: AppConstants.defaultPadding),

                // Nutrition breakdown chart
                _buildNutritionBreakdownChart(context, goals, progress),

                const SizedBox(height: AppConstants.defaultPadding),

                // Progress chart based on selected period
                _buildProgressChart(context),

                const SizedBox(height: AppConstants.defaultPadding),

                // Micronutrient tracking
                _buildMicronutrientSection(context, goals),

                const SizedBox(height: AppConstants.defaultPadding),

                // Meal timing analysis
                _buildMealTimingAnalysis(context),

                const SizedBox(height: AppConstants.defaultPadding),

                // Food variety tracking
                _buildFoodVarietySection(context),

                const SizedBox(height: AppConstants.defaultPadding),

                // Insights and recommendations
                _buildInsightsSection(context, goals, progress),
              ] else ...[
                _buildNoDataMessage(context),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return CustomCard(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Nutrition Analytics',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Track your nutrition trends and insights',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Time Period',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: _periods.map((period) {
              final isSelected = period == _selectedPeriod;
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: CustomButton(
                    text: period,
                    isOutlined: !isSelected,
                    onPressed: () {
                      setState(() {
                        _selectedPeriod = period;
                      });
                      _loadAnalyticsData();
                    },
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTodaysOverview(
    BuildContext context,
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    final currentData = context.watch<NutritionProvider>().currentDayData;
    final totalNutrition = currentData?.dailyLog.totalNutrition;

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          if (totalNutrition != null) ...[
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Calories',
                    totalNutrition.calories.toStringAsFixed(0),
                    goals.dailyCalories.toStringAsFixed(0),
                    progress['calories'] ?? 0,
                    Icons.local_fire_department,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Protein',
                    '${totalNutrition.protein.toStringAsFixed(1)}g',
                    '${goals.dailyProtein.toStringAsFixed(1)}g',
                    progress['protein'] ?? 0,
                    Icons.fitness_center,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Carbs',
                    '${totalNutrition.carbs.toStringAsFixed(1)}g',
                    '${goals.dailyCarbs.toStringAsFixed(1)}g',
                    progress['carbs'] ?? 0,
                    Icons.grain,
                    Colors.amber,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Fats',
                    '${totalNutrition.fats.toStringAsFixed(1)}g',
                    '${goals.dailyFats.toStringAsFixed(1)}g',
                    progress['fats'] ?? 0,
                    Icons.opacity,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ] else ...[
            const Center(child: Text('No nutrition data for today')),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String current,
    String target,
    double percentage,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            current,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            'of $target',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${percentage.toStringAsFixed(0)}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: percentage > 100 ? Colors.red : color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionBreakdownChart(
    BuildContext context,
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Weekly Macronutrient Breakdown',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _getWeeklyMacroPieChartSections(context),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Legend
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem(context, 'Protein', Colors.red),
              _buildLegendItem(context, 'Carbs', Colors.amber),
              _buildLegendItem(context, 'Fats', Colors.blue),
            ],
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _getWeeklyMacroPieChartSections(
    BuildContext context,
  ) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final weeklyMacros = nutritionProvider.getWeeklyMacroBreakdown();

    final protein = weeklyMacros['protein'] ?? 0;
    final carbs = weeklyMacros['carbs'] ?? 0;
    final fats = weeklyMacros['fats'] ?? 0;

    final total = protein + carbs + fats;
    if (total == 0) {
      return [
        PieChartSectionData(
          color: Colors.grey,
          value: 100,
          title: 'No Data',
          radius: 50,
        ),
      ];
    }

    return [
      PieChartSectionData(
        color: Colors.red,
        value: protein,
        title: '${(protein / total * 100).toStringAsFixed(0)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.amber,
        value: carbs,
        title: '${(carbs / total * 100).toStringAsFixed(0)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.blue,
        value: fats,
        title: '${(fats / total * 100).toStringAsFixed(0)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 4),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildProgressChart(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$_selectedPeriod Progress',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: true),
                titlesData: FlTitlesData(
                  leftTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 40),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          _getXAxisLabel(value.toInt()),
                          style: Theme.of(context).textTheme.bodySmall,
                        );
                      },
                    ),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: true),
                lineBarsData: [
                  LineChartBarData(
                    spots: _getCalorieSpots(context),
                    isCurved: true,
                    color: Colors.orange,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                  LineChartBarData(
                    spots: _getProteinSpots(context),
                    isCurved: true,
                    color: Colors.red,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                  LineChartBarData(
                    spots: _getCarbSpots(context),
                    isCurved: true,
                    color: Colors.amber,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                  LineChartBarData(
                    spots: _getFatSpots(context),
                    isCurved: true,
                    color: Colors.blue,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // Legend for multiple lines
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem(context, 'Calories', Colors.orange),
              _buildLegendItem(context, 'Protein', Colors.red),
              _buildLegendItem(context, 'Carbs', Colors.amber),
              _buildLegendItem(context, 'Fats', Colors.blue),
            ],
          ),

          const SizedBox(height: AppConstants.smallPadding),

          Text(
            'Nutrition trends over the selected period',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _getXAxisLabel(int index) {
    switch (_selectedPeriod) {
      case 'Week':
        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        return index >= 0 && index < days.length ? days[index] : '';
      case 'Month':
        return '${index + 1}';
      case '3 Months':
        return 'W${index + 1}';
      default:
        return '';
    }
  }

  List<FlSpot> _getCalorieSpots(BuildContext context) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final data = _getPeriodData(nutritionProvider);
    
    return data.asMap().entries.map((entry) {
      final calories = entry.value.fold<double>(
        0, (sum, foodEntry) => sum + foodEntry.nutritionData.calories);
      return FlSpot(entry.key.toDouble(), calories);
    }).toList();
  }

  List<FlSpot> _getProteinSpots(BuildContext context) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final data = _getPeriodData(nutritionProvider);
    
    return data.asMap().entries.map((entry) {
      final protein = entry.value.fold<double>(
        0, (sum, foodEntry) => sum + foodEntry.nutritionData.protein);
      return FlSpot(entry.key.toDouble(), protein);
    }).toList();
  }

  List<FlSpot> _getCarbSpots(BuildContext context) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final data = _getPeriodData(nutritionProvider);
    
    return data.asMap().entries.map((entry) {
      final carbs = entry.value.fold<double>(
        0, (sum, foodEntry) => sum + foodEntry.nutritionData.carbs);
      return FlSpot(entry.key.toDouble(), carbs);
    }).toList();
  }

  List<FlSpot> _getFatSpots(BuildContext context) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final data = _getPeriodData(nutritionProvider);
    
    return data.asMap().entries.map((entry) {
      final fats = entry.value.fold<double>(
        0, (sum, foodEntry) => sum + foodEntry.nutritionData.fats);
      return FlSpot(entry.key.toDouble(), fats);
    }).toList();
  }

  List<List<FoodEntry>> _getPeriodData(NutritionProvider nutritionProvider) {
    switch (_selectedPeriod) {
      case 'Week':
        return nutritionProvider.getWeeklyGroupedData();
      case 'Month':
        return nutritionProvider.getMonthlyGroupedData();
      case '3 Months':
        return nutritionProvider.getQuarterlyGroupedData();
      default:
        return [];
    }
  }

  Widget _buildInsightsSection(
    BuildContext context,
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    final insights = _generateInsights(goals, progress);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Insights & Recommendations',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          ...insights.map(
            (insight) => Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(insight.icon, color: insight.color, size: 20),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          insight.title,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          insight.description,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<NutritionInsight> _generateInsights(
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    final insights = <NutritionInsight>[];
    final nutritionProvider = context.watch<NutritionProvider>();
    final currentData = nutritionProvider.currentDayData;
    final totalNutrition = currentData?.dailyLog.totalNutrition;

    final calorieProgress = progress['calories'] ?? 0;
    final proteinProgress = progress['protein'] ?? 0;
    final carbProgress = progress['carbs'] ?? 0;
    final fatProgress = progress['fats'] ?? 0;
    final fiberProgress = progress['fiber'] ?? 0;
    final sodiumProgress = progress['sodium'] ?? 0;

    // Calorie insights
    if (calorieProgress < 80) {
      insights.add(
        NutritionInsight(
          icon: Icons.warning,
          color: Colors.orange,
          title: 'Low Calorie Intake',
          description:
              'You\'re ${(80 - calorieProgress).toStringAsFixed(0)}% below your calorie goal. Add nutrient-dense snacks like nuts, yogurt, or fruit.',
        ),
      );
    } else if (calorieProgress > 120) {
      insights.add(
        NutritionInsight(
          icon: Icons.info,
          color: Colors.blue,
          title: 'High Calorie Intake',
          description:
              'You\'ve exceeded your calorie goal by ${(calorieProgress - 100).toStringAsFixed(0)}%. Consider portion control or increase physical activity.',
        ),
      );
    }

    // Protein insights
    if (proteinProgress < 70) {
      insights.add(
        NutritionInsight(
          icon: Icons.fitness_center,
          color: Colors.red,
          title: 'Increase Protein',
          description:
              'You need ${((goals.dailyProtein * 0.7) - (totalNutrition?.protein ?? 0)).toStringAsFixed(0)}g more protein. Try chicken, fish, eggs, or plant-based options.',
        ),
      );
    } else if (proteinProgress > 150) {
      insights.add(
        NutritionInsight(
          icon: Icons.fitness_center,
          color: Colors.orange,
          title: 'High Protein Intake',
          description:
              'Your protein intake is ${(proteinProgress - 100).toStringAsFixed(0)}% above target. Ensure adequate hydration.',
        ),
      );
    }

    // Carbohydrate insights
    if (carbProgress > 150) {
      insights.add(
        NutritionInsight(
          icon: Icons.grain,
          color: Colors.amber,
          title: 'High Carb Intake',
          description:
              'Consider replacing refined carbs with whole grains, vegetables, and fruits for better nutrition.',
        ),
      );
    } else if (carbProgress < 50) {
      insights.add(
        NutritionInsight(
          icon: Icons.grain,
          color: Colors.green,
          title: 'Low Carb Approach',
          description:
              'You\'re following a lower-carb pattern. Ensure you\'re getting enough fiber from vegetables.',
        ),
      );
    }

    // Fat insights
    if (fatProgress < 50) {
      insights.add(
        NutritionInsight(
          icon: Icons.opacity,
          color: Colors.blue,
          title: 'Increase Healthy Fats',
          description:
              'Add ${((goals.dailyFats * 0.5) - (totalNutrition?.fats ?? 0)).toStringAsFixed(0)}g more healthy fats: avocados, nuts, olive oil, or fatty fish.',
        ),
      );
    }

    // Fiber insights
    if (fiberProgress < 60) {
      insights.add(
        NutritionInsight(
          icon: Icons.grass,
          color: Colors.brown,
          title: 'Boost Fiber Intake',
          description:
              'Add more vegetables, fruits, whole grains, and legumes to reach your fiber goal.',
        ),
      );
    }

    // Sodium insights
    if (sodiumProgress > 120) {
      insights.add(
        NutritionInsight(
          icon: Icons.water_drop,
          color: Colors.red,
          title: 'High Sodium Intake',
          description:
              'Your sodium is ${(sodiumProgress - 100).toStringAsFixed(0)}% above recommended. Reduce processed foods and add more fresh ingredients.',
        ),
      );
    }

    // Micronutrient insights
    if (totalNutrition != null) {
      if (totalNutrition.vitaminC < 50) {
        insights.add(
          NutritionInsight(
            icon: Icons.local_pharmacy,
            color: Colors.orange,
            title: 'Low Vitamin C',
            description:
                'Include citrus fruits, berries, bell peppers, or broccoli for immune support.',
          ),
        );
      }

      if (totalNutrition.calcium < 500) {
        insights.add(
          NutritionInsight(
            icon: Icons.healing,
            color: Colors.green,
            title: 'Boost Calcium',
            description:
                'Add dairy products, leafy greens, or fortified plant milks for bone health.',
          ),
        );
      }

      if (totalNutrition.iron < 8) {
        insights.add(
          NutritionInsight(
            icon: Icons.fitness_center,
            color: Colors.red,
            title: 'Iron Deficiency Risk',
            description:
                'Include lean meats, spinach, lentils, or fortified cereals. Pair with vitamin C for better absorption.',
          ),
        );
      }
    }

    // Meal timing insights
    final mealData = nutritionProvider.getMealTimingData();
    final breakfastCalories = mealData['breakfast'] ?? 0;
    final dinnerCalories = mealData['dinner'] ?? 0;

    if (breakfastCalories < 200) {
      insights.add(
        NutritionInsight(
          icon: Icons.wb_sunny,
          color: Colors.yellow,
          title: 'Light Breakfast',
          description:
              'A substantial breakfast can boost metabolism and energy levels throughout the day.',
        ),
      );
    }

    if (dinnerCalories > breakfastCalories * 2) {
      insights.add(
        NutritionInsight(
          icon: Icons.nightlight,
          color: Colors.indigo,
          title: 'Heavy Evening Meals',
          description:
              'Consider shifting more calories to earlier in the day for better digestion and sleep.',
        ),
      );
    }

    // Food variety insights
    final varietyData = nutritionProvider.getFoodVarietyData();
    final uniqueFoods = varietyData['uniqueFoods'] as int? ?? 0;

    if (uniqueFoods < 10) {
      insights.add(
        NutritionInsight(
          icon: Icons.restaurant_menu,
          color: Colors.purple,
          title: 'Increase Food Variety',
          description:
              'Try to include ${15 - uniqueFoods} more different foods this week for better nutrient diversity.',
        ),
      );
    }

    // Positive reinforcement
    if (insights.isEmpty || insights.length < 2) {
      insights.add(
        NutritionInsight(
          icon: Icons.check_circle,
          color: Colors.green,
          title: 'Excellent Nutrition Balance!',
          description:
              'You\'re maintaining great nutritional balance. Keep up the consistent healthy eating habits!',
        ),
      );
    }

    return insights;
  }

  Widget _buildMicronutrientSection(BuildContext context, NutritionalGoals goals) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final currentData = nutritionProvider.currentDayData;
    final totalNutrition = currentData?.dailyLog.totalNutrition;

    if (totalNutrition == null) {
      return const SizedBox.shrink();
    }

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Micronutrients & Vitamins',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Vitamins row
          Row(
            children: [
              Expanded(
                child: _buildMicronutrientItem(
                  context, 'Vitamin A', '${totalNutrition.vitaminA.toStringAsFixed(0)} mcg',
                  Icons.visibility, Colors.purple,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildMicronutrientItem(
                  context, 'Vitamin C', '${totalNutrition.vitaminC.toStringAsFixed(0)} mg',
                  Icons.local_pharmacy, Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          // Minerals row
          Row(
            children: [
              Expanded(
                child: _buildMicronutrientItem(
                  context, 'Calcium', '${totalNutrition.calcium.toStringAsFixed(0)} mg',
                  Icons.healing, Colors.green,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildMicronutrientItem(
                  context, 'Iron', '${totalNutrition.iron.toStringAsFixed(1)} mg',
                  Icons.fitness_center, Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          // Additional nutrients
          Row(
            children: [
              Expanded(
                child: _buildMicronutrientItem(
                  context, 'Fiber', '${totalNutrition.fiber.toStringAsFixed(1)} g',
                  Icons.grass, Colors.brown,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildMicronutrientItem(
                  context, 'Sodium', '${totalNutrition.sodium.toStringAsFixed(0)} mg',
                  Icons.water_drop, Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMicronutrientItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMealTimingAnalysis(BuildContext context) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final mealData = nutritionProvider.getMealTimingData();

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Meal Timing Analysis',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: mealData.values.isNotEmpty 
                    ? mealData.values.reduce((a, b) => a > b ? a : b) * 1.2
                    : 100,
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        const meals = ['Breakfast', 'Lunch', 'Dinner', 'Snacks'];
                        if (value.toInt() >= 0 && value.toInt() < meals.length) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              meals[value.toInt()],
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 40),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _getMealTimingBarGroups(mealData),
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Average calories consumed per meal type',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  List<BarChartGroupData> _getMealTimingBarGroups(Map<String, double> mealData) {
    final meals = ['breakfast', 'lunch', 'dinner', 'snack'];
    final colors = [Colors.orange, Colors.green, Colors.blue, Colors.purple];
    
    return meals.asMap().entries.map((entry) {
      final index = entry.key;
      final mealType = entry.value;
      final calories = mealData[mealType] ?? 0;
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: calories,
            color: colors[index],
            width: 20,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      );
    }).toList();
  }

  Widget _buildFoodVarietySection(BuildContext context) {
    final nutritionProvider = context.watch<NutritionProvider>();
    final varietyData = nutritionProvider.getFoodVarietyData();

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Food Variety & Patterns',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          Row(
            children: [
              Expanded(
                child: _buildVarietyMetric(
                  context,
                  'Unique Foods',
                  varietyData['uniqueFoods']?.toString() ?? '0',
                  Icons.restaurant_menu,
                  Colors.green,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildVarietyMetric(
                  context,
                  'Food Categories',
                  varietyData['categories']?.toString() ?? '0',
                  Icons.category,
                  Colors.blue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          Row(
            children: [
              Expanded(
                child: _buildVarietyMetric(
                  context,
                  'Avg Meals/Day',
                  varietyData['avgMealsPerDay']?.toStringAsFixed(1) ?? '0.0',
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildVarietyMetric(
                  context,
                  'Most Common',
                  varietyData['mostCommonFood']?.toString() ?? 'None',
                  Icons.star,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVarietyMetric(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataMessage(BuildContext context) {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Analytics Available',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Start tracking your nutrition to see detailed analytics and insights.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomButton(
            text: 'Add Food Entry',
            icon: Icons.add,
            onPressed: () {
              // Navigate to camera or add food page
              Navigator.pushNamed(context, '/camera');
            },
          ),
        ],
      ),
    );
  }
}

class NutritionInsight {
  final IconData icon;
  final Color color;
  final String title;
  final String description;

  NutritionInsight({
    required this.icon,
    required this.color,
    required this.title,
    required this.description,
  });
}
