import 'package:flutter/material.dart';

class OnboardingBackground extends StatelessWidget {
  final Widget child;
  final int currentPage;

  const OnboardingBackground({
    super.key,
    required this.child,
    required this.currentPage,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 800),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: _getGradientColors(theme, currentPage),
        ),
      ),
      child: Stack(
        children: [
          // Floating circles for visual interest
          ..._buildFloatingCircles(theme, currentPage),
          
          // Main content
          child,
        ],
      ),
    );
  }

  List<Color> _getGradientColors(ThemeData theme, int page) {
    switch (page) {
      case 0:
        return [
          theme.colorScheme.primary.withValues(alpha: 0.1),
          theme.colorScheme.secondary.withValues(alpha: 0.05),
          Colors.white,
        ];
      case 1:
        return [
          theme.colorScheme.secondary.withValues(alpha: 0.1),
          theme.colorScheme.tertiary.withValues(alpha: 0.05),
          Colors.white,
        ];
      case 2:
        return [
          theme.colorScheme.tertiary.withValues(alpha: 0.1),
          theme.colorScheme.primary.withValues(alpha: 0.05),
          Colors.white,
        ];
      default:
        return [
          theme.colorScheme.primary.withValues(alpha: 0.1),
          Colors.white,
        ];
    }
  }

  List<Widget> _buildFloatingCircles(ThemeData theme, int page) {
    return [
      // Top right circle
      Positioned(
        top: -50,
        right: -50,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 1000),
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.primary.withValues(alpha: 0.05),
          ),
        ),
      ),
      
      // Bottom left circle
      Positioned(
        bottom: -100,
        left: -100,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 1200),
          width: 300,
          height: 300,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.secondary.withValues(alpha: 0.03),
          ),
        ),
      ),
      
      // Middle right small circle
      Positioned(
        top: 200,
        right: -30,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 800),
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.tertiary.withValues(alpha: 0.04),
          ),
        ),
      ),
    ];
  }
}