# NutriAI 🥗

An AI-powered nutritional tracking Flutter app with beautiful, modern UI/UX and intelligent food image analysis.

## ✨ Features

- **AI Photo Analysis**: Snap photos of your meals and get instant nutritional information using Gemini AI
- **Smart Tracking**: Monitor your daily nutrition goals with intuitive progress indicators
- **Personalized Goals**: Set custom nutrition targets based on your profile and activity level
- **Beautiful Analytics**: Comprehensive nutrition insights with interactive charts
- **Modern UI/UX**: Clean, Material 3 design with smooth animations and micro-interactions

## 🎨 Recent UI/UX Improvements

### Modern Onboarding Experience
- **Animated Progress Indicators**: Beautiful step indicators with smooth transitions
- **Gradient Backgrounds**: Dynamic gradient backgrounds that change per page
- **Floating Elements**: Subtle floating circles for visual depth
- **Interactive Forms**: Modern form fields with focus animations and micro-interactions
- **Success Animation**: Delightful completion animation with custom checkmark painter
- **Haptic Feedback**: Tactile feedback for better user engagement

### Enhanced Visual Design
- **Material 3 Design**: Latest Material Design principles with dynamic theming
- **Smooth Animations**: Page transitions, form interactions, and loading states
- **Modern Cards**: Elevated cards with proper shadows and rounded corners
- **Interactive Elements**: Hover effects, scale animations, and visual feedback
- **Consistent Spacing**: Proper spacing system using design tokens

### Improved User Experience
- **Form Validation**: Real-time validation with helpful error messages
- **Loading States**: Beautiful loading indicators and skeleton screens
- **Accessibility**: Proper contrast ratios and semantic markup
- **Responsive Design**: Optimized for different screen sizes
- **Performance**: Optimized animations and efficient rendering

## 🏗️ Architecture

The app follows Clean Architecture principles with:

```
lib/
├── core/                 # Core utilities and constants
│   ├── constants/       # App constants and configuration
│   ├── routes/          # Navigation and routing
│   └── widgets/         # Reusable UI components
├── data/                # Data layer
│   ├── datasources/     # API and database sources
│   ├── models/          # Data models
│   └── repositories/    # Repository implementations
├── domain/              # Business logic layer
│   ├── entities/        # Domain entities
│   ├── repositories/    # Repository interfaces
│   └── usecases/        # Business use cases
└── presentation/        # UI layer
    ├── pages/           # Screen widgets
    ├── providers/       # State management
    └── widgets/         # Custom UI components
```

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (>=3.8.1)
- Dart SDK
- Android Studio / VS Code
- Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nutri_ai
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API Key**
   - Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Update `lib/core/constants/app_constants.dart`:
   ```dart
   static const String geminiApiKey = 'YOUR_API_KEY_HERE';
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 📱 Screenshots

### Onboarding Flow
- Welcome screen with feature highlights
- Personal information collection with modern forms
- Goal setting with interactive selection cards
- Success animation on completion

### Main Features
- Dashboard with nutrition overview
- Camera integration for food analysis
- Progress tracking and analytics
- Profile management

## 🛠️ Key Dependencies

- **flutter**: UI framework
- **provider**: State management
- **sqflite**: Local database
- **http/dio**: API communication
- **image_picker**: Camera integration
- **fl_chart**: Data visualization
- **shared_preferences**: Local storage
- **permission_handler**: Device permissions

## 🎯 Development Focus

### Performance
- Optimized animations with proper disposal
- Efficient state management
- Lazy loading and caching
- Memory leak prevention

### User Experience
- Intuitive navigation flow
- Consistent design language
- Accessibility compliance
- Error handling and recovery

### Code Quality
- Clean Architecture principles
- Comprehensive error handling
- Type safety with null safety
- Consistent code formatting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Google Gemini AI for food analysis capabilities
- Flutter team for the amazing framework
- Material Design team for design guidelines
- Open source community for inspiration and tools
